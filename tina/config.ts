import { defineConfig } from "tinacms";

// Your hosting provider likely exposes this as an environment variable
const branch =
  process.env.GITHUB_BRANCH ||
  process.env.VERCEL_GIT_COMMIT_REF ||
  process.env.HEAD ||
  "main";

export default defineConfig({
  branch,

  // Get this from tina.io
  clientId: process.env.NEXT_PUBLIC_TINA_CLIENT_ID,
  // Get this from tina.io
  token: process.env.TINA_TOKEN,

  build: {
    outputFolder: "admin",
    publicFolder: "public",
  },
  media: {
    tina: {
      mediaRoot: "images",
      publicFolder: "public",
    },
  },
  // See docs on content modeling for more info on how to setup new content models: https://tina.io/docs/schema/
  schema: {
    collections: [
      // Translations
      {
        name: "UI",
        label: "UI Translations",
        path: "src/i18n/locales",
        format: "json",
        ui: {
          allowedActions: {
            create: false,
            delete: false,
          },
        },
        fields: [
          {
            type: "object",
            name: "site",
            label: "Site",
            fields: [
              {
                type: "string",
                name: "title",
                label: "Site Title",
              },
              {
                type: "string",
                name: "description",
                label: "Site Description",
                ui: {
                  component: "textarea",
                },
              },
            ],
          },
          {
            type: "object",
            name: "nav",
            label: "Navigation",
            fields: [
              {
                type: "string",
                name: "features",
                label: "Features",
              },
              {
                type: "string",
                name: "download",
                label: "Download",
              },
              {
                type: "string",
                name: "darkMode",
                label: "Dark Mode",
              },
              {
                type: "string",
                name: "language",
                label: "Language",
              },
            ],
          },
          {
            type: "object",
            name: "hero",
            label: "Hero Section",
            fields: [
              {
                type: "object",
                name: "title",
                label: "Title",
                fields: [
                  {
                    type: "string",
                    name: "start",
                    label: "Start",
                  },
                  {
                    type: "string",
                    name: "end",
                    label: "End",
                  },
                ],
              },
              {
                type: "string",
                name: "subtitle",
                label: "Subtitle",
                ui: {
                  component: "textarea",
                },
              },
              {
                type: "string",
                name: "getStarted",
                label: "Get Started Button",
              },
              {
                type: "string",
                name: "learnMore",
                label: "Learn More Button",
              },
            ],
          },
          {
            type: "object",
            name: "features",
            label: "Features Section",
            fields: [
              {
                type: "object",
                name: "title",
                label: "Title",
                fields: [
                  {
                    type: "string",
                    name: "start",
                    label: "Start",
                  },
                  {
                    type: "string",
                    name: "end",
                    label: "End",
                  },
                ],
              },
              {
                type: "string",
                name: "subtitle",
                label: "Subtitle",
                ui: {
                  component: "textarea",
                },
              },
              {
                type: "object",
                name: "billTracking",
                label: "Bill Tracking Feature",
                fields: [
                  {
                    type: "string",
                    name: "title",
                    label: "Title",
                  },
                  {
                    type: "string",
                    name: "description",
                    label: "Description",
                    ui: {
                      component: "textarea",
                    },
                  },
                ],
              },
              {
                type: "object",
                name: "chartVisualizations",
                label: "Chart Visualizations Feature",
                fields: [
                  {
                    type: "string",
                    name: "title",
                    label: "Title",
                  },
                  {
                    type: "string",
                    name: "description",
                    label: "Description",
                    ui: {
                      component: "textarea",
                    },
                  },
                ],
              },
              {
                type: "object",
                name: "serviceProvider",
                label: "Service Provider Feature",
                fields: [
                  {
                    type: "string",
                    name: "title",
                    label: "Title",
                  },
                  {
                    type: "string",
                    name: "description",
                    label: "Description",
                    ui: {
                      component: "textarea",
                    },
                  },
                ],
              },
              {
                type: "object",
                name: "multiLanguage",
                label: "Multi-language Feature",
                fields: [
                  {
                    type: "string",
                    name: "title",
                    label: "Title",
                  },
                  {
                    type: "string",
                    name: "description",
                    label: "Description",
                    ui: {
                      component: "textarea",
                    },
                  },
                ],
              },
            ],
          },
          {
            type: "object",
            name: "download",
            label: "Download Section",
            fields: [
              {
                type: "object",
                name: "title",
                label: "Title",
                fields: [
                  {
                    type: "string",
                    name: "start",
                    label: "Start",
                  },
                  {
                    type: "string",
                    name: "end",
                    label: "End",
                  },
                ],
              },
              {
                type: "string",
                name: "subtitle",
                label: "Subtitle",
                ui: {
                  component: "textarea",
                },
              },
              {
                type: "string",
                name: "appStore",
                label: "App Store",
              },
              {
                type: "string",
                name: "googlePlay",
                label: "Google Play",
              },
              {
                type: "string",
                name: "downloadNow",
                label: "Download Now",
              },
            ],
          },
          {
            type: "object",
            name: "footer",
            label: "Footer Section",
            fields: [
              {
                type: "string",
                name: "tagline",
                label: "Tagline",
                ui: {
                  component: "textarea",
                },
              },
              {
                type: "string",
                name: "product",
                label: "Product",
              },
              {
                type: "string",
                name: "company",
                label: "Company",
              },
              {
                type: "string",
                name: "legal",
                label: "Legal",
              },
              {
                type: "string",
                name: "about",
                label: "About",
              },
              {
                type: "string",
                name: "blog",
                label: "Blog",
              },
              {
                type: "string",
                name: "careers",
                label: "Careers",
              },
              {
                type: "string",
                name: "pricing",
                label: "Pricing",
              },
              {
                type: "string",
                name: "privacy",
                label: "Privacy",
              },
              {
                type: "string",
                name: "terms",
                label: "Terms",
              },
              {
                type: "string",
                name: "contact",
                label: "Contact",
              },
              {
                type: "string",
                name: "copyright",
                label: "Copyright",
              },
            ],
          },
        ],
      },
    ],
  },
});
