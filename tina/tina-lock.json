{"schema": {"version": {"fullVersion": "1.5.16", "major": "1", "minor": "5", "patch": "16"}, "meta": {"flags": ["experimentalData"]}, "collections": [{"name": "UI", "label": "UI Translations", "path": "src/i18n/locales", "format": "json", "ui": {"allowedActions": {"create": false, "delete": false}}, "fields": [{"type": "object", "name": "site", "label": "Site", "fields": [{"type": "string", "name": "title", "label": "Site Title", "namespace": ["UI", "site", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Site Description", "ui": {"component": "textarea"}, "namespace": ["UI", "site", "description"], "searchable": true, "uid": false}], "namespace": ["UI", "site"], "searchable": true, "uid": false}, {"type": "object", "name": "nav", "label": "Navigation", "fields": [{"type": "string", "name": "features", "label": "Features", "namespace": ["UI", "nav", "features"], "searchable": true, "uid": false}, {"type": "string", "name": "download", "label": "Download", "namespace": ["UI", "nav", "download"], "searchable": true, "uid": false}, {"type": "string", "name": "darkMode", "label": "Dark Mode", "namespace": ["UI", "nav", "darkMode"], "searchable": true, "uid": false}, {"type": "string", "name": "language", "label": "Language", "namespace": ["UI", "nav", "language"], "searchable": true, "uid": false}], "namespace": ["UI", "nav"], "searchable": true, "uid": false}, {"type": "object", "name": "hero", "label": "Hero Section", "fields": [{"type": "object", "name": "title", "label": "Title", "fields": [{"type": "string", "name": "start", "label": "Start", "namespace": ["UI", "hero", "title", "start"], "searchable": true, "uid": false}, {"type": "string", "name": "end", "label": "End", "namespace": ["UI", "hero", "title", "end"], "searchable": true, "uid": false}], "namespace": ["UI", "hero", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "subtitle", "label": "Subtitle", "ui": {"component": "textarea"}, "namespace": ["UI", "hero", "subtitle"], "searchable": true, "uid": false}, {"type": "string", "name": "getStarted", "label": "Get Started But<PERSON>", "namespace": ["UI", "hero", "getStarted"], "searchable": true, "uid": false}, {"type": "string", "name": "learnMore", "label": "<PERSON>rn <PERSON>", "namespace": ["UI", "hero", "learnMore"], "searchable": true, "uid": false}], "namespace": ["UI", "hero"], "searchable": true, "uid": false}, {"type": "object", "name": "features", "label": "Features Section", "fields": [{"type": "object", "name": "title", "label": "Title", "fields": [{"type": "string", "name": "start", "label": "Start", "namespace": ["UI", "features", "title", "start"], "searchable": true, "uid": false}, {"type": "string", "name": "end", "label": "End", "namespace": ["UI", "features", "title", "end"], "searchable": true, "uid": false}], "namespace": ["UI", "features", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "subtitle", "label": "Subtitle", "ui": {"component": "textarea"}, "namespace": ["UI", "features", "subtitle"], "searchable": true, "uid": false}, {"type": "object", "name": "billTracking", "label": "<PERSON>ing Feature", "fields": [{"type": "string", "name": "title", "label": "Title", "namespace": ["UI", "features", "billTracking", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "ui": {"component": "textarea"}, "namespace": ["UI", "features", "billTracking", "description"], "searchable": true, "uid": false}], "namespace": ["UI", "features", "billTracking"], "searchable": true, "uid": false}, {"type": "object", "name": "chartVisualizations", "label": "Chart Visualizations Feature", "fields": [{"type": "string", "name": "title", "label": "Title", "namespace": ["UI", "features", "chartVisualizations", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "ui": {"component": "textarea"}, "namespace": ["UI", "features", "chartVisualizations", "description"], "searchable": true, "uid": false}], "namespace": ["UI", "features", "chartVisualizations"], "searchable": true, "uid": false}, {"type": "object", "name": "serviceProvider", "label": "Service Provider Feature", "fields": [{"type": "string", "name": "title", "label": "Title", "namespace": ["UI", "features", "serviceProvider", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "ui": {"component": "textarea"}, "namespace": ["UI", "features", "serviceProvider", "description"], "searchable": true, "uid": false}], "namespace": ["UI", "features", "serviceProvider"], "searchable": true, "uid": false}, {"type": "object", "name": "multiLanguage", "label": "Multi-language Feature", "fields": [{"type": "string", "name": "title", "label": "Title", "namespace": ["UI", "features", "multiLanguage", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "description", "label": "Description", "ui": {"component": "textarea"}, "namespace": ["UI", "features", "multiLanguage", "description"], "searchable": true, "uid": false}], "namespace": ["UI", "features", "multiLanguage"], "searchable": true, "uid": false}], "namespace": ["UI", "features"], "searchable": true, "uid": false}, {"type": "object", "name": "download", "label": "Download Section", "fields": [{"type": "object", "name": "title", "label": "Title", "fields": [{"type": "string", "name": "start", "label": "Start", "namespace": ["UI", "download", "title", "start"], "searchable": true, "uid": false}, {"type": "string", "name": "end", "label": "End", "namespace": ["UI", "download", "title", "end"], "searchable": true, "uid": false}], "namespace": ["UI", "download", "title"], "searchable": true, "uid": false}, {"type": "string", "name": "subtitle", "label": "Subtitle", "ui": {"component": "textarea"}, "namespace": ["UI", "download", "subtitle"], "searchable": true, "uid": false}, {"type": "string", "name": "appStore", "label": "App Store", "namespace": ["UI", "download", "appStore"], "searchable": true, "uid": false}, {"type": "string", "name": "googlePlay", "label": "Google Play", "namespace": ["UI", "download", "googlePlay"], "searchable": true, "uid": false}, {"type": "string", "name": "downloadNow", "label": "Download Now", "namespace": ["UI", "download", "downloadNow"], "searchable": true, "uid": false}], "namespace": ["UI", "download"], "searchable": true, "uid": false}, {"type": "object", "name": "footer", "label": "Footer Section", "fields": [{"type": "string", "name": "tagline", "label": "Tagline", "ui": {"component": "textarea"}, "namespace": ["UI", "footer", "tagline"], "searchable": true, "uid": false}, {"type": "string", "name": "product", "label": "Product", "namespace": ["UI", "footer", "product"], "searchable": true, "uid": false}, {"type": "string", "name": "company", "label": "Company", "namespace": ["UI", "footer", "company"], "searchable": true, "uid": false}, {"type": "string", "name": "legal", "label": "Legal", "namespace": ["UI", "footer", "legal"], "searchable": true, "uid": false}, {"type": "string", "name": "about", "label": "About", "namespace": ["UI", "footer", "about"], "searchable": true, "uid": false}, {"type": "string", "name": "blog", "label": "Blog", "namespace": ["UI", "footer", "blog"], "searchable": true, "uid": false}, {"type": "string", "name": "careers", "label": "Careers", "namespace": ["UI", "footer", "careers"], "searchable": true, "uid": false}, {"type": "string", "name": "pricing", "label": "Pricing", "namespace": ["UI", "footer", "pricing"], "searchable": true, "uid": false}, {"type": "string", "name": "privacy", "label": "Privacy", "namespace": ["UI", "footer", "privacy"], "searchable": true, "uid": false}, {"type": "string", "name": "terms", "label": "Terms", "namespace": ["UI", "footer", "terms"], "searchable": true, "uid": false}, {"type": "string", "name": "contact", "label": "Contact", "namespace": ["UI", "footer", "contact"], "searchable": true, "uid": false}, {"type": "string", "name": "copyright", "label": "Copyright", "namespace": ["UI", "footer", "copyright"], "searchable": true, "uid": false}], "namespace": ["UI", "footer"], "searchable": true, "uid": false}], "namespace": ["UI"]}], "config": {"media": {"tina": {"publicFolder": "public", "mediaRoot": "images"}}}}, "lookup": {"DocumentConnection": {"type": "DocumentConnection", "resolveType": "multiCollectionDocumentList", "collections": ["UI"]}, "Node": {"type": "Node", "resolveType": "nodeDocument"}, "DocumentNode": {"type": "DocumentNode", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "UI": {"type": "UI", "resolveType": "collectionDocument", "collection": "UI", "createUI": "create", "updateUI": "update"}, "UIConnection": {"type": "UIConnection", "resolveType": "collectionDocumentList", "collection": "UI"}}, "graphql": {"kind": "Document", "definitions": [{"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "Reference"}, "description": {"kind": "StringValue", "value": "References another document, used as a foreign key"}, "directives": []}, {"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "JSON"}, "description": {"kind": "StringValue", "value": ""}, "directives": []}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SystemInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "filename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "basename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasReferences"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "breadcrumbs"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "excludeExtension"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relativePath"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "extension"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "template"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Folder"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasPreviousPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasNextPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "startCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "endCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Node"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Document"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": "A relay-compliant pagination connection"}, "name": {"kind": "Name", "value": "Connection"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Query"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "getOptimizedQuery"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "queryString"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collections"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "id"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "UI"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UI"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "UIConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIConnection"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "UI"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "DocumentConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "DocumentConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Collection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "slug"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "format"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "matches"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "templates"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "fields"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "documents"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "folder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnection"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "DocumentNode"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "UI"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Folder"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UISite"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UINav"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "features"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "download"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "darkMode"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "language"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIHeroTitle"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "start"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "end"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIHero"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIHeroTitle"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "subtitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "getStarted"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "learnMore"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIFeaturesTitle"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "start"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "end"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIFeaturesBillTracking"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIFeaturesChartVisualizations"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIFeaturesServiceProvider"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIFeaturesMultiLanguage"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIFeatures"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesTitle"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "subtitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "billTracking"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesBillTracking"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "chartVisualizations"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesChartVisualizations"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "serviceProvider"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesServiceProvider"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "multiLanguage"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesMultiLanguage"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIDownloadTitle"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "start"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "end"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIDownload"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIDownloadTitle"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "subtitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "appStore"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "googlePlay"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "downloadNow"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tagline"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "product"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "company"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "legal"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "about"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "blog"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "careers"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pricing"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "privacy"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "terms"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "contact"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "copyright"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "UI"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "site"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UISite"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "nav"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UINav"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hero"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIHero"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "features"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeatures"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "download"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIDownload"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "footer"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "StringFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UISiteFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UINavFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "features"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "download"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "darkMode"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "language"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIHeroTitleFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "start"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "end"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIHeroFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIHeroTitleFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "getStarted"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "learnMore"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesTitleFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "start"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "end"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesBillTrackingFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesChartVisualizationsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesServiceProviderFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesMultiLanguageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesTitleFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "billTracking"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesBillTrackingFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "chartVisualizations"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesChartVisualizationsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "serviceProvider"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesServiceProviderFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "multiLanguage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesMultiLanguageFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIDownloadTitleFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "start"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "end"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIDownloadFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIDownloadTitleFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "appStore"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "googlePlay"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "downloadNow"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIF<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tagline"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "product"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "company"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legal"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "about"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blog"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careers"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pricing"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "privacy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "terms"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "contact"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "copyright"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "site"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UISiteFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "nav"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UINavFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIHeroFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "features"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "download"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIDownloadFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIF<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "UIConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UI"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "UIConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Mutation"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "addPendingDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "template"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "deleteDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createFolder"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateUI"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UI"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createUI"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UI"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "UI"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "UI"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UISiteMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UINavMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "features"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "download"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "darkMode"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "language"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIHeroTitleMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "start"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "end"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIHeroMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIHeroTitleMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "getStarted"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "learnMore"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesTitleMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "start"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "end"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesBillTrackingMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesChartVisualizationsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesServiceProviderMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesMultiLanguageMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFeaturesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesTitleMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "billTracking"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesBillTrackingMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "chartVisualizations"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesChartVisualizationsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "serviceProvider"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesServiceProviderMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "multiLanguage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesMultiLanguageMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIDownloadTitleMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "start"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "end"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIDownloadMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIDownloadTitleMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "appStore"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "googlePlay"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "downloadNow"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIFooterMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tagline"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "product"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "company"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legal"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "about"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blog"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careers"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pricing"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "privacy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "terms"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "contact"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "copyright"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "UIMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "site"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UISiteMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "nav"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UINavMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIHeroMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "features"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFeaturesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "download"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIDownloadMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "UIFooterMutation"}}}]}]}}