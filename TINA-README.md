# TinaCMS Integration for Optilife Landing Page

This document provides instructions on how to use TinaCMS to manage content for the Optilife landing page.

## Setup

1. Create an account on [TinaCMS](https://tina.io/)
2. Create a new project in TinaCMS
3. Get your Client ID and Token from TinaCMS
4. Create a `.env` file in the root of the project (copy from `.env.example`)
5. Add your Client ID and Token to the `.env` file:

```
NEXT_PUBLIC_TINA_CLIENT_ID=your-client-id-here
TINA_TOKEN=your-token-here
```

## Running the CMS

To run the CMS locally:

```bash
pnpm dev
```

This will start both the Astro development server and the TinaCMS server.

## Accessing the CMS

The CMS is available at:

```
http://localhost:4001/admin/
```

## Content Structure

The content is organized as follows:

1. **Site Settings** - Global site settings like URL, logo, etc.
2. **Translations** - Translations for each supported language:
   - English (EN)
   - Serbian (SR)
   - Serbian Latin (SR Latin)
   - Russian (RU)
3. **Posts** - Blog posts or other content

## Editing Content

### Translations

1. Go to the TinaCMS admin panel
2. Select the language you want to edit
3. Edit the content
4. Save your changes

### Site Settings

1. Go to the TinaCMS admin panel
2. Select "Site Settings"
3. Edit the global site settings
4. Save your changes

## Deploying with TinaCMS

When deploying your site with TinaCMS:

1. Make sure to set the environment variables in your hosting provider
2. Build the site with `pnpm build`
3. The content will be fetched from TinaCMS during the build process

## Troubleshooting

If you encounter issues:

1. Make sure your environment variables are set correctly
2. Check that you have the correct permissions in TinaCMS
3. Try restarting the development server
4. Check the console for error messages

## Additional Resources

- [TinaCMS Documentation](https://tina.io/docs/)
- [Astro Documentation](https://docs.astro.build/)
