// @ts-check
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'astro/config';

// https://astro.build/config
export default defineConfig({
  vite: {
    plugins: [tailwindcss()],
    // Add environment variables to be available in the client
    define: {
      'process.env.NEXT_PUBLIC_TINA_CLIENT_ID': JSON.stringify(process.env.NEXT_PUBLIC_TINA_CLIENT_ID),
    },
  },

  // Add TinaCMS integration
  integrations: [],

  // Configure server options
  server: {
    // Configure headers to allow TinaCMS to connect to the Astro dev server
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  }
});