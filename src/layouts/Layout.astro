---
import logoSquare from '../assets/logo_square.svg';
import logoSquarePng from '../assets/logo_square.png';
import '../styles/global.css';
import { getLangFromUrl, useTranslations } from '../i18n/utils';

// Get the language from props or URL
const { lang = getLangFromUrl(Astro.url) } = Astro.props;
const t = useTranslations(lang);

// Define site metadata for social sharing
const siteName = "Optilife";
const siteTitle = t('site.title');
const siteDescription = t('site.description');
const siteUrl = "https://welcome.optilife.house";

// Define the HTML lang attribute
const htmlLang = lang && lang.includes('_') ? lang.split('_')[0] : lang || 'en'; // For sr_Latn, use 'sr' as the HTML lang
---
<!doctype html>
<html lang={htmlLang}>
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta
		  name="description"
		  content={siteDescription}
		/>
		<title>{siteTitle}</title>
		<link rel="icon" type="image/svg+xml" href={logoSquare.src} />
		<meta name="generator" content={Astro.generator} />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={siteUrl} />
		<meta property="og:title" content={siteTitle} />
		<meta property="og:description" content={siteDescription} />
		<meta property="og:image" content={new URL(logoSquarePng.src, siteUrl).toString()} />
		<meta property="og:image:alt" content={`${siteName} logo`} />
		<meta property="og:site_name" content={siteName} />

		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={siteUrl} />
		<meta property="twitter:title" content={siteTitle} />
		<meta property="twitter:description" content={siteDescription} />
		<meta property="twitter:image" content={new URL(logoSquarePng.src, siteUrl).toString()} />
		<meta property="twitter:image:alt" content={`${siteName} logo`} />

		<!-- Theme Color for browsers -->
		<meta name="theme-color" content="#50dcae" />
	</head>
	<body
    class="bg-background-light dark:bg-background-dark text-gray-800 dark:text-gray-200"
  >
    <slot />
  </body>
</html>

<script>
	// Apply theme from localStorage or system preference immediately to prevent flash
	const html = document.documentElement;
	const savedTheme = localStorage.getItem("darkMode");

	if (savedTheme === "true") {
	  html.classList.add("dark");
	} else if (savedTheme === "false") {
	  html.classList.remove("dark");
	} else if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
	  html.classList.add("dark");
	}

	// Simple function to toggle dark mode
	function toggleDarkMode() {
	  if (html.classList.contains("dark")) {
		html.classList.remove("dark");
		localStorage.setItem("darkMode", "false");
	  } else {
		html.classList.add("dark");
		localStorage.setItem("darkMode", "true");
	  }
	}

	// Wait for DOM to be fully loaded
	document.addEventListener("DOMContentLoaded", function () {
	  // Set up dark mode toggle
	  const darkModeToggle = document.getElementById("darkModeToggle");
	  if (darkModeToggle) {
		darkModeToggle.addEventListener("click", toggleDarkMode);
	  }

	  // Add smooth scrolling for anchor links
	  document.querySelectorAll("a[href^='#']").forEach(function (anchor) {
		anchor.addEventListener("click", function (e) {
		  e.preventDefault();
		  // Use the event target instead of 'this' to fix TypeScript issue
		  const targetId = (e.currentTarget as HTMLAnchorElement).getAttribute("href") || "";
		  const targetElement = document.querySelector(targetId);

		  if (targetElement) {
			window.scrollTo({
			  top: (targetElement as HTMLElement).offsetTop - 80, // Offset for header
			  behavior: "smooth",
			});
		  }
		});
	  });
	});
</script>