---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<div>
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
        {t('utilityControl.title.start')}
        <span class="text-primary">
          {t('utilityControl.title.end')}
        </span>
      </h2>
      <p class="mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
        {t('utilityControl.subtitle')}
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
      <!-- Financial Savings -->
      <div class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full">
        <div class="rounded-full bg-primary/10 dark:bg-primary/20 p-4 w-16 h-16 flex items-center justify-center mb-4">
          <!-- Money/Savings Icon -->
          <svg
            class="w-8 h-8 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2" />
            <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" />
            <path d="M14 11h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5" />
            <path d="M12 17v1m0 -8v1" />
          </svg>
        </div>
        <div class="flex flex-col flex-grow">
          <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
            {t('utilityControl.financialSavings.title')}
          </h3>
          <p class="text-gray-600 dark:text-gray-300">
            {t('utilityControl.financialSavings.description')}
          </p>
        </div>
      </div>

      <!-- Environmental Impact -->
      <div class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full">
        <div class="rounded-full bg-secondary/10 dark:bg-secondary/20 p-4 w-16 h-16 flex items-center justify-center mb-4">
          <!-- Environment Icon -->
          <svg
            class="w-8 h-8 text-secondary"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M12 18.5l-3 1.5l.5 -3.5l-2 -2l3 -.5l1.5 -3l1.5 3l3 .5l-2 2l.5 3.5z" />
            <path d="M12 3a9 9 0 0 1 9 9" />
            <path d="M12 3a9 9 0 0 0 -9 9" />
          </svg>
        </div>
        <div class="flex flex-col flex-grow">
          <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
            {t('utilityControl.environmentalImpact.title')}
          </h3>
          <p class="text-gray-600 dark:text-gray-300">
            {t('utilityControl.environmentalImpact.description')}
          </p>
        </div>
      </div>

      <!-- Resource Efficiency -->
      <div class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full">
        <div class="rounded-full bg-primary/10 dark:bg-primary/20 p-4 w-16 h-16 flex items-center justify-center mb-4">
          <!-- Efficiency Icon -->
          <svg
            class="w-8 h-8 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M3 12h3" />
            <path d="M12 3v3" />
            <path d="M7.8 7.8l-2.2 -2.2" />
            <path d="M16.2 7.8l2.2 -2.2" />
            <path d="M7.8 16.2l-2.2 2.2" />
            <path d="M12 12l9 3l-4 2l-2 4l-3 -9" />
          </svg>
        </div>
        <div class="flex flex-col flex-grow">
          <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
            {t('utilityControl.resourceEfficiency.title')}
          </h3>
          <p class="text-gray-600 dark:text-gray-300">
            {t('utilityControl.resourceEfficiency.description')}
          </p>
        </div>
      </div>

      <!-- Future Planning -->
      <div class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full">
        <div class="rounded-full bg-secondary/10 dark:bg-secondary/20 p-4 w-16 h-16 flex items-center justify-center mb-4">
          <!-- Planning Icon -->
          <svg
            class="w-8 h-8 text-secondary"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M9 4h6a2 2 0 0 1 2 2v14l-5 -3l-5 3v-14a2 2 0 0 1 2 -2" />
          </svg>
        </div>
        <div class="flex flex-col flex-grow">
          <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
            {t('utilityControl.futurePlanning.title')}
          </h3>
          <p class="text-gray-600 dark:text-gray-300">
            {t('utilityControl.futurePlanning.description')}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
