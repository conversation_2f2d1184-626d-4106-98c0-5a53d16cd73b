---
import { Image } from 'astro:assets';
import logoRound from '../assets/logo_round.svg';
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---
<div class="relative overflow-hidden">
    <div
      class="max-w-5xl mx-auto flex flex-col md:flex-row items-center justify-between gap-12"
    >
      <div class="text-left md:w-1/2 animate-fade-in">
        <h1
          class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight"
        >
          <span class="text-primary">
            {t('hero.title.start')}
          </span>
          {t('hero.title.end')}
        </h1>
        <p
          class="mt-6 text-xl text-gray-600 dark:text-gray-300 leading-relaxed"
        >
          {t('hero.subtitle')}
        </p>
        <div class="mt-10 flex gap-4">
          <a
            href="#download"
            class="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-all shadow-md hover:shadow-lg transform hover:-translate-y-1"
          >
            {t('hero.getStarted')}
          </a>
          <a
            href="#features"
            class="border border-primary text-primary hover:bg-primary/10 px-8 py-3 rounded-lg transition-all"
          >
            {t('hero.learnMore')}
          </a>
        </div>
      </div>
      <div class="md:w-1/2 flex justify-center animate-slide-up">
        <div class="relative">
          <div
            class="absolute inset-0 bg-linear-to-br from-primary/20 to-secondary/20 rounded-full blur-3xl"
          ></div>
          <Image
            src={logoRound}
            alt="Optilife Logo"
            class="relative mx-auto h-64 md:h-80 drop-shadow-xl transform transition-all hover:scale-105 duration-500 rounded-full"
          />
        </div>
      </div>
    </div>
</div>
