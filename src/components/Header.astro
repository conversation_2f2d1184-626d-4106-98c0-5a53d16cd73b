---
import { Image } from 'astro:assets';
import logoSquare from '../assets/logo_square.svg';
import LanguagePicker from './LanguagePicker.astro';
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---
<header
class="py-4 px-6 md:px-12 flex justify-between items-center bg-white dark:bg-gray-800 shadow-soft sticky top-0 z-50 transition-all"
>
    <a href={`/${lang}`} id="backToTop" class="flex items-center space-x-2 cursor-pointer">
        <Image
            src={logoSquare}
            alt="Optilife Logo"
            class="h-10 w-10 rounded-lg transition-transform duration-300 hover:scale-105"
        />
        <span
            class="text-xl font-semibold text-primary-dark dark:text-primary-light hover:text-primary transition-colors duration-200"
            >Optilife</span
        >
    </a>

    <!-- Mobile menu button -->
    <button
        id="mobileMenuButton"
        class="md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
        aria-label="Open menu"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <!-- Desktop navigation -->
    <nav class="hidden md:flex items-center gap-6">
        <a
            href={`/${lang}#features`}
            class="px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-primary transition-all relative group"
        >
            {t('nav.features')}
            <span
            class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"
            ></span>
        </a>
        <a
            href={`/${lang}#download`}
            class="px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-primary transition-all relative group"
        >
            {t('nav.download')}
            <span
            class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"
            ></span>
        </a>
        <LanguagePicker />
        <button
            id="darkModeToggle"
            class="cursor-pointer p-2 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all ml-2"
            aria-label="Toggle dark mode"
        >
            <svg
            class="w-5 h-5 hidden dark:block text-yellow-400"
            fill="currentColor"
            viewBox="0 0 20 20"
            >
            <!-- Sun icon -->
            <path
                d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
            />
            </svg>
            <svg
            class="w-5 h-5 dark:hidden text-gray-600"
            fill="currentColor"
            viewBox="0 0 20 20"
            >
            <!-- Moon icon -->
            <path
                d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
            />
            </svg>
        </button>
    </nav>

    <!-- Mobile navigation menu (hidden by default) -->
    <div id="mobileMenu" class="fixed top-0 right-0 z-50 bg-white dark:bg-gray-800 shadow-xl rounded-bl-2xl hidden opacity-0 transform translate-x-full transition-all duration-200 ease-out">
        <div class="flex flex-col w-64 max-w-full h-auto py-6 px-6">
            <button
                id="closeMenuButton"
                class="self-end p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none mb-4"
                aria-label="Close menu"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <div class="flex flex-col space-y-4">
                <a
                    href={`/${lang}#features`}
                    class="px-3 py-4 text-lg text-gray-600 dark:text-gray-300 hover:text-primary border-b border-gray-200 dark:border-gray-700 transition-colors duration-200"
                >
                    {t('nav.features')}
                </a>
                <a
                    href={`/${lang}#download`}
                    class="px-3 py-4 text-lg text-gray-600 dark:text-gray-300 hover:text-primary border-b border-gray-200 dark:border-gray-700 transition-colors duration-200"
                >
                    {t('nav.download')}
                </a>
                <div class="border-b border-gray-200 dark:border-gray-700 py-4">
                    <LanguagePicker isMobile={true} />
                </div>
                <div class="flex items-center px-3 py-4">
                    <button
                        id="darkModeToggleWrapper"
                        class="flex items-center w-full cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg px-2 py-1 transition-colors duration-200"
                        aria-label="Toggle dark mode"
                    >
                        <span class="text-gray-600 dark:text-gray-300 mr-3">{t('nav.darkMode')}</span>
                        <span
                            id="mobileDarkModeToggle"
                            class="p-2 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 inline-flex"
                        >
                            <svg
                            class="w-5 h-5 hidden dark:block text-yellow-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            >
                            <!-- Sun icon -->
                            <path
                                d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                            />
                            </svg>
                            <svg
                            class="w-5 h-5 dark:hidden text-gray-600"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            >
                            <!-- Moon icon -->
                            <path
                                d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
                            />
                            </svg>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
    // Wait for DOM to be fully loaded
    document.addEventListener("DOMContentLoaded", function () {
        // Mobile menu functionality
        const mobileMenuButton = document.getElementById("mobileMenuButton");
        const closeMenuButton = document.getElementById("closeMenuButton");
        const mobileMenu = document.getElementById("mobileMenu");
        const mobileDarkModeToggle = document.getElementById("mobileDarkModeToggle");
        const darkModeToggle = document.getElementById("darkModeToggle");

        // Toggle mobile menu with smooth animation
        if (mobileMenuButton && mobileMenu && closeMenuButton) {
            // Function to close the menu
            function closeMenu() {
                // Check if mobileMenu exists to satisfy TypeScript
                if (!mobileMenu) return;

                // Start the animation out immediately
                mobileMenu.classList.add("opacity-0", "translate-x-full");

                // Use transitionend instead of setTimeout for more reliable timing
                const handleTransitionEnd = () => {
                    if (!mobileMenu) return;
                    mobileMenu.classList.remove("flex");
                    mobileMenu.classList.add("hidden");
                    document.body.style.overflow = ""; // Re-enable scrolling
                    mobileMenu.removeEventListener("transitionend", handleTransitionEnd);
                };

                mobileMenu.addEventListener("transitionend", handleTransitionEnd);
            }

            // Open the menu
            mobileMenuButton.addEventListener("click", function(e) {
                // Make menu visible and start animation immediately
                mobileMenu.classList.remove("hidden");
                mobileMenu.classList.add("flex");

                // Trigger animation on next frame for better performance
                requestAnimationFrame(() => {
                    mobileMenu.classList.remove("opacity-0", "translate-x-full");
                });

                document.body.style.overflow = "hidden"; // Prevent scrolling when menu is open

                // Prevent the click from propagating to the document
                e.stopPropagation();
            });

            // Close button
            closeMenuButton.addEventListener("click", function(e) {
                closeMenu();
                e.stopPropagation(); // Prevent propagation
            });

            // Close menu when clicking on a link
            const mobileMenuLinks = mobileMenu.querySelectorAll("a");
            mobileMenuLinks.forEach(link => {
                link.addEventListener("click", closeMenu);
            });

            // Close menu when clicking outside
            document.addEventListener("click", function(e) {
                // If the menu is open and the click is outside the menu
                if (!mobileMenu.classList.contains("hidden") &&
                    e.target instanceof Node && !mobileMenu.contains(e.target) &&
                    e.target !== mobileMenuButton) {
                    closeMenu();
                }
            });

            // Prevent clicks inside the menu from closing it
            mobileMenu.addEventListener("click", function(e) {
                e.stopPropagation();
            });
        }

        // Sync dark mode toggles
        const darkModeToggleWrapper = document.getElementById("darkModeToggleWrapper");

        if (darkModeToggleWrapper) {
            darkModeToggleWrapper.addEventListener("click", toggleDarkMode);
        } else if (mobileDarkModeToggle && darkModeToggle) {
            // Fallback to the old approach if wrapper isn't found
            mobileDarkModeToggle.addEventListener("click", toggleDarkMode);
        }

        // Back to top functionality
        const backToTopLink = document.getElementById("backToTop");
        if (backToTopLink) {
            backToTopLink.addEventListener("click", function(e) {
                // Check if we're on the homepage
                const isHomePage = window.location.pathname.split('/').filter(Boolean).length <= 1;

                if (isHomePage) {
                    // If we're on the homepage, just scroll to top
                    e.preventDefault();
                    window.scrollTo({
                        top: 0,
                        behavior: "smooth"
                    });
                }
                // If we're not on the homepage, let the default link behavior happen
                // which will navigate to the homepage
            });
        }
    });

    // Dark mode toggle function (already defined in Layout.astro)
    function toggleDarkMode() {
        const html = document.documentElement;
        if (html.classList.contains("dark")) {
            html.classList.remove("dark");
            localStorage.setItem("darkMode", "false");
        } else {
            html.classList.add("dark");
            localStorage.setItem("darkMode", "true");
        }
    }
</script>
