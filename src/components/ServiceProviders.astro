---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Fetch service providers data
const response = await fetch(new URL('/service-providers.json', Astro.url));
const serviceProviders = await response.json();

// Utility type icons mapping
const utilityTypeIcons = {
  GAS: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-flame" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
    <path d="M12 12c2 -2.96 0 -7 -1 -8c0 3.038 -1.773 4.741 -3 6c-1.226 1.26 -2 3.24 -2 5a6 6 0 1 0 12 0c0 -1.532 -1.056 -3.94 -2 -5c-1.786 3 -3 4 -4 2z"></path>
  </svg>`,
  WATER: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-droplet" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
    <path d="M7.502 19.423c2.602 2.105 6.395 2.105 8.996 0c2.602 -2.105 3.262 -5.708 1.566 -8.546l-4.89 -7.26c-.42 -.625 -1.287 -.803 -1.936 -.397a1.376 1.376 0 0 0 -.41 .397l-4.893 7.26c-1.695 2.838 -1.035 6.441 1.567 8.546z"></path>
  </svg>`,
  ELECTRICITY: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-bolt" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
    <path d="M13 3l0 7l6 0l-8 11l0 -7l-6 0l8 -11"></path>
  </svg>`,
  GARBAGE: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-trash" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
    <path d="M4 7l16 0"></path>
    <path d="M10 11l0 6"></path>
    <path d="M14 11l0 6"></path>
    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>
    <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>
  </svg>`,
  MUNICIPAL_SERVICES: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-building" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
    <path d="M3 21l18 0"></path>
    <path d="M9 8l1 0"></path>
    <path d="M9 12l1 0"></path>
    <path d="M9 16l1 0"></path>
    <path d="M14 8l1 0"></path>
    <path d="M14 12l1 0"></path>
    <path d="M14 16l1 0"></path>
    <path d="M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16"></path>
  </svg>`
};

// Support status badge colors
const supportStatusColors = {
  FULL: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  PARTIAL: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
};
---

<div>
  <div class="max-w-5xl mx-auto">
    <div class="text-center mb-12">
      <h2
        class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white"
      >
        {t('serviceProviders.title.start')}
        <span class="text-primary">
          {t('serviceProviders.title.end')}
        </span>
      </h2>
      <p
        class="mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
      >
        {t('serviceProviders.subtitle')}
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      {serviceProviders.map((provider) => (
        <div class="bg-white dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300">
          <div class="flex justify-between items-start mb-4">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{provider.name}</h3>
            <span class={`px-3 py-1 rounded-full text-sm font-medium ${supportStatusColors[provider.support]}`}>
              {t(`serviceProviders.support.${provider.support}`)}
            </span>
          </div>

          <div class="flex flex-wrap gap-3 mt-4">
            {provider.utility_types.map((type) => (
              <div class="flex items-center bg-gray-100 dark:bg-gray-600 px-3 py-2 rounded-lg">
                <div class="text-primary dark:text-primary-dark" set:html={utilityTypeIcons[type]} />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{t(`serviceProviders.utilityTypes.${type}`)}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
</div>
