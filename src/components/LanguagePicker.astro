---
import { languages, languageAbbreviations, getLocalizedPathname, getLangFromUrl, useTranslations } from '../i18n/utils';

// Get current URL to determine current language and path
const { pathname } = Astro.url;
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Check if this is being used in mobile view
const { isMobile = false } = Astro.props;
---

{!isMobile ? (
  <div class="relative inline-block text-left">
    <div>
      <button
        type="button"
        class="cursor-pointer inline-flex items-center justify-center w-full rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-xs font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        id="language-menu-button"
        aria-expanded="false"
        aria-haspopup="true"
      >
        <svg
          class="w-5 h-5 mr-2"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M4 5h7" />
          <path d="M9 3v2c0 4.418 -2.239 8 -5 8" />
          <path d="M5 9c0 2.144 2.952 3.908 6.7 4" />
          <path d="M12 20l4 -9l4 9" />
          <path d="M19.1 18h-6.2" />
        </svg>
        <span>{languageAbbreviations[lang]}</span>
        <svg
          class="-mr-1 ml-2 h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>

    <div
      class="hidden origin-top-right absolute right-0 mt-2 w-32 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="language-menu-button"
      tabindex="-1"
      id="language-menu"
    >
      <div class="py-1" role="none">
        {
          Object.entries(languages).map(([langCode]) => (
            <a
              href={getLocalizedPathname(pathname, langCode as keyof typeof languages)}
              class="text-gray-700 dark:text-gray-200 block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
              role="menuitem"
              tabindex="-1"
            >
              {languageAbbreviations[langCode as keyof typeof languageAbbreviations]}
            </a>
          ))
        }
      </div>
    </div>
  </div>
) : (
  <div class="mobile-language-picker w-full">
    <div class="flex items-center mb-2">
      <svg
        class="w-5 h-5 mr-2 text-gray-600 dark:text-gray-300"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
        <path d="M4 5h7" />
        <path d="M9 3v2c0 4.418 -2.239 8 -5 8" />
        <path d="M5 9c0 2.144 2.952 3.908 6.7 4" />
        <path d="M12 20l4 -9l4 9" />
        <path d="M19.1 18h-6.2" />
      </svg>
      <span class="text-gray-600 dark:text-gray-300 font-medium">{t('nav.language')}</span>
    </div>
    <div class="grid grid-cols-1 gap-2">
      {
        Object.entries(languages).map(([langCode]) => (
          <a
            href={getLocalizedPathname(pathname, langCode as keyof typeof languages)}
            class={`text-center py-2 px-3 rounded-md text-sm transition-colors duration-200 ${langCode === lang ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'}`}
          >
            {languageAbbreviations[langCode as keyof typeof languageAbbreviations]}
          </a>
        ))
      }
    </div>
  </div>
)}

<script>
  // Toggle language menu (for desktop version only)
  const button = document.getElementById('language-menu-button');
  const menu = document.getElementById('language-menu');

  if (button && menu) {
    button.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent event from bubbling up
      menu.classList.toggle('hidden');
    });

    // Close the menu when clicking outside
    document.addEventListener('click', (event) => {
      if (
        !menu.contains(event.target as Node) &&
        !button.contains(event.target as Node) &&
        !menu.classList.contains('hidden')
      ) {
        menu.classList.add('hidden');
      }
    });
  }
</script>
