---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<div>
<div class="max-w-5xl mx-auto">
  <div class="text-center mb-12">
	<h2
	  class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white"
	>
	  {t('download.title.start')}
	  <span class="text-primary">
		{t('download.title.end')}
	  </span>
	</h2>
	<p
	  class="mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
	>
	  {t('download.subtitle')}
	</p>
  </div>

  <div
	class="flex flex-col md:flex-row justify-center items-center gap-8"
  >
	<div
	  class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 max-w-sm w-full"
	>
	  <div class="flex items-center justify-center mb-6">
		<svg
		  class="w-12 h-12 text-black dark:text-white"
		  viewBox="0 0 24 24"
		  fill="currentColor"
		>
		  <path
			d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.21 2.33-.91 3.57-.84 1.5.12 2.63.64 3.4 1.8-3.03 1.81-2.52 5.68.24 7.16-.62 1.81-1.42 3.6-2.29 5.05zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.26 2.01-1.16 4.07-3.74 4.25z"
		  />
		</svg>
		<span class="ml-3 text-xl font-semibold">{t('download.appStore')}</span>
	  </div>
	  <a
	  	href="https://apps.apple.com/app/optilife/id6743851332"
		target="_blank"
		class="w-full bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg transition-all shadow-md hover:shadow-lg transform hover:-translate-y-1 flex items-center justify-center"
	  >
		<span>{t('download.downloadNow')}</span>
		<svg
		  class="w-5 h-5 ml-2"
		  fill="none"
		  stroke="currentColor"
		  viewBox="0 0 24 24"
		>
		  <path
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke-width="2"
			d="M17 8l4 4m0 0l-4 4m4-4H3"
		  ></path>
		</svg>
	</a>
	</div>

	<div
	  class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 max-w-sm w-full"
	>
	  <div class="flex items-center justify-center mb-6">
		<svg
		  class="w-12 h-12 text-primary"
		  viewBox="0 0 24 24"
		  fill="currentColor"
		>
		  <path
			d="M3.61 1.814L13.75 12l-10.14 10.186c-.31-.298-.61-.734-.61-1.323V3.137c0-.589.3-1.025.61-1.323zM15.24 13.49l3.11 3.11-9.21 5.22c-.47.27-1.26.16-1.74-.22l8.28-8.31-.44.2zm.87-1.73l-2.69-2.69 8.5-8.5c.48.38.76 1.06.49 1.73l-6.3 9.46zm-4.81-4.81L8.61 4.27l9.21 5.22c.47.27.76.95.49 1.42l-7.01-4.05z"
		  />
		</svg>
		<span class="ml-3 text-xl font-semibold">{t('download.googlePlay')}</span>
	  </div>
	  <a
		  href="https://play.google.com/store/apps/details?id=house.optilife.optilife"
		  target="_blank"
		  class="w-full bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg transition-all shadow-md hover:shadow-lg transform hover:-translate-y-1 flex items-center justify-center"
	  >
		<span>{t('download.downloadNow')}</span>
		<svg
		  class="w-5 h-5 ml-2"
		  fill="none"
		  stroke="currentColor"
		  viewBox="0 0 24 24"
		>
		  <path
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke-width="2"
			d="M17 8l4 4m0 0l-4 4m4-4H3"
		  ></path>
		</svg>
	  </a>
	</div>
  </div>
</div>
</div>
