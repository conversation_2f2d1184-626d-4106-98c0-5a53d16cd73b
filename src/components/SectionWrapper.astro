---
// SectionWrapper.astro - A component to provide alternating backgrounds for sections
// Props:
// - index: The index of the section (required)
// - id: Optional ID for the section
// - class: Optional additional classes

const { index, id, class: additionalClass = "" } = Astro.props;

// Determine background class based on index (even/odd)
const isEven = index % 2 === 0;
const bgClass = isEven
  ? "bg-linear-to-br from-primary/5 to-secondary/5 dark:from-primary-dark/10 dark:to-secondary-dark/10"
  : "bg-white dark:bg-gray-800";

// Combine all classes
const combinedClass = `py-24 px-6 ${bgClass} ${additionalClass}`;
---

<section id={id} class={combinedClass}>
  <slot />
</section>
