---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<div>
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h2
          class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white"
        >
          {t('features.title.start')}
          <span class="text-primary">
            {t('features.title.end')}
          </span>
        </h2>
        <p
          class="mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
        >
          {t('features.subtitle')}
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-8 max-w-5xl mx-auto">
        <div
          class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full"
        >
          <div
            class="rounded-full bg-secondary/10 dark:bg-secondary/20 p-4 w-16 h-16 flex items-center justify-center mb-4"
          >
            <!-- Tabler Icon: Receipt -->
            <svg
              class="w-8 h-8 text-secondary"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
              <path d="M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2" />
              <path d="M14 8h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5" />
              <path d="M12 7v1m0 8v1" />
            </svg>
          </div>
          <div class="flex flex-col flex-grow">
            <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
              {t('features.billTracking.title')}
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              {t('features.billTracking.description')}
            </p>
          </div>
        </div>

        <div
          class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full"
        >
          <div
            class="rounded-full bg-primary/10 dark:bg-primary/20 p-4 w-16 h-16 flex items-center justify-center mb-4"
          >
            <!-- Tabler Icon: Chart Bar -->
            <svg
              class="w-8 h-8 text-primary"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
              <path d="M3 12m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
              <path d="M9 8m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
              <path d="M15 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
              <path d="M4 20l14 0" />
            </svg>
          </div>
          <div class="flex flex-col flex-grow">
            <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
              {t('features.chartVisualizations.title')}
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              {t('features.chartVisualizations.description')}
            </p>
          </div>
        </div>
        <div
        class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full"
      >
        <div
          class="rounded-full bg-primary/10 dark:bg-primary/20 p-4 w-16 h-16 flex items-center justify-center mb-4"
        >
          <!-- Tabler Icon: Address Book -->
          <svg
            class="w-8 h-8 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M20 6v12a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2z" />
            <path d="M10 16h6" />
            <path d="M13 11m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
            <path d="M4 8h3" />
            <path d="M4 12h3" />
            <path d="M4 16h3" />
          </svg>
        </div>
        <div class="flex flex-col flex-grow">
          <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
            {t('features.serviceProvider.title')}
          </h3>
          <p class="text-gray-600 dark:text-gray-300">
            {t('features.serviceProvider.description')}
          </p>
        </div>
      </div>

        <div
          class="bg-background-light dark:bg-gray-700 p-6 rounded-xl shadow-soft transition-all hover:shadow-lg hover:-translate-y-1 duration-300 flex flex-col h-full"
        >
          <div
            class="rounded-full bg-secondary/10 dark:bg-secondary/20 p-4 w-16 h-16 flex items-center justify-center mb-4"
          >
            <!-- Tabler Icon: Language -->
            <svg
              class="w-8 h-8 text-secondary"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
              <path d="M4 5h7" />
              <path d="M9 3v2c0 4.418 -2.239 8 -5 8" />
              <path d="M5 9c0 2.144 2.952 3.908 6.7 4" />
              <path d="M12 20l4 -9l4 9" />
              <path d="M19.1 18h-6.2" />
            </svg>
          </div>
          <div class="flex flex-col flex-grow">
            <h3 class="font-bold text-xl text-gray-900 dark:text-white mb-3">
              {t('features.multiLanguage.title')}
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              {t('features.multiLanguage.description')}
            </p>
          </div>
        </div>
      </div>
    </div>
</div>
