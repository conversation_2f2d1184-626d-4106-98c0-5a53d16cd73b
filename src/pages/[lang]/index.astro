---
import Download from '../../components/Download.astro';
import Features from '../../components/Features.astro';
import Footer from '../../components/Footer.astro';
import Header from '../../components/Header.astro';
import Hero from '../../components/Hero.astro';
import Layout from '../../layouts/Layout.astro';
import SectionWrapper from '../../components/SectionWrapper.astro';
import ServiceProviders from '../../components/ServiceProviders.astro';
import UtilityControl from '../../components/UtilityControl.astro';
import { languages, defaultLang } from '../../i18n/utils';

// Get the language from the URL
export function getStaticPaths() {
  const paths = Object.keys(languages).map(lang => ({
    params: { lang }
  }));

  return paths;
}

const { lang } = Astro.params;
---

<Layout lang={lang}>
	<Header />
	<SectionWrapper index={0}>
		<Hero />
	</SectionWrapper>
	<SectionWrapper index={1} id="utility-control">
		<UtilityControl />
	</SectionWrapper>
	<SectionWrapper index={2} id="features">
		<Features />
	</SectionWrapper>
	<SectionWrapper index={3} id="service-providers">
		<ServiceProviders />
	</SectionWrapper>
	<SectionWrapper index={4} id="download">
		<Download />
	</SectionWrapper>
	<Footer />
</Layout>
