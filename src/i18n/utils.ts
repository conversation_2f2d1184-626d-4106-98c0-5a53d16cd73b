import en from "./locales/en.json";
import ru from "./locales/ru.json";
import sr from "./locales/sr.json";
import sr_Latn from "./locales/sr_Latn.json";

// Define the supported languages
export const languages = {
  en: "English",
  sr: "Српски",
  sr_Latn: "<PERSON><PERSON><PERSON> (latinica)",
  ru: "Русский",
};

// Define language abbreviations
export const languageAbbreviations = {
  en: "EN",
  sr: "SRB-ЋИР",
  sr_Latn: "SRB-LAT",
  ru: "RU",
};

// Define the default language
export const defaultLang = "en";

// Create a type for our UI dictionary based on the English version
export type UiDict = typeof en;

// Create a dictionary of all translations
export const ui = {
  en,
  ru,
  sr,
  sr_Latn,
};

// Get the preferred language from the URL (/en/page, /ru/page, etc.)
export function getLangFromUrl(url: URL) {
  const [, lang] = url.pathname.split("/");
  if (lang in ui) return lang as keyof typeof ui;
  return defaultLang;
}

// Get translated content
export function useTranslations(lang: keyof typeof ui) {
  return function t(key: string) {
    // Split the key by dots to access nested properties
    const keys = key.split(".");
    let value: any = ui[lang];

    // Navigate through the nested properties
    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k];
      } else {
        // If the key doesn't exist in the current language, try the default language
        if (lang !== defaultLang) {
          let defaultValue: any = ui[defaultLang];
          for (const dk of keys) {
            if (
              defaultValue &&
              typeof defaultValue === "object" &&
              dk in defaultValue
            ) {
              defaultValue = defaultValue[dk];
            } else {
              return key; // Return the key itself if not found in default language
            }
          }
          return defaultValue;
        }
        return key; // Return the key itself if not found
      }
    }

    return value;
  };
}

// Get a URL for a specific language
export function getLocalizedPathname(pathname: string, lang: keyof typeof ui) {
  // Remove the language prefix if it exists
  const segments = pathname.split("/");
  if (segments[1] in ui) {
    segments.splice(1, 1);
  }

  // Add the new language prefix
  if (lang !== defaultLang) {
    segments.splice(1, 0, lang);
  }

  return segments.join("/") || "/";
}
